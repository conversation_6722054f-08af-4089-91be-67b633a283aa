#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QWidget>
#include <QTimer>
#include <QLabel>
#include <QPushButton>
#include <QHBoxLayout>
#include <QtCharts/QChartView>
#include <QtCharts/QLineSeries>
#include <QtCharts/QChart>
#include <QtCharts/QValueAxis>
#include <QTime>
#include <QDebug>
#include <QList>
#include <QPointF>

QT_CHARTS_USE_NAMESPACE

class ChartPerformanceTest : public QMainWindow
{
    Q_OBJECT

public:
    ChartPerformanceTest(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        setupChart();
        setupTimer();
    }

private slots:
    void addDataPoint()
    {
        static int pointCount = 0;
        static QTime startTime = QTime::currentTime();
        
        // 模拟数据采集间隔（15秒）
        double timeMinutes = pointCount * (15.0 / 60.0);  // 15秒间隔转换为分钟
        double o2Value = 15.0 + 5.0 * qSin(pointCount * 0.1);  // 模拟O2数据
        double coValue = 100.0 + 50.0 * qCos(pointCount * 0.15);  // 模拟CO数据
        
        // 使用高效的replace方法而不是append
        if (pointCount % 10 == 0 || m_dataPoints.size() >= MAX_WINDOW_POINTS) {
            // 每10个点或达到窗口大小时使用replace方法
            updateWithReplace(timeMinutes, o2Value, coValue);
        } else {
            // 否则使用append
            m_o2Series->append(timeMinutes, o2Value);
            m_coSeries->append(timeMinutes, coValue);
        }
        
        // 更新X轴范围（10分钟滑动窗口）
        updateAxisRange(timeMinutes);
        
        pointCount++;
        
        // 性能统计
        if (pointCount % 20 == 0) {
            int elapsedMs = startTime.msecsTo(QTime::currentTime());
            double pointsPerSecond = pointCount * 1000.0 / elapsedMs;
            m_statusLabel->setText(QString("数据点: %1, 性能: %.1f点/秒, 图表点数: %2")
                                  .arg(pointCount).arg(pointsPerSecond).arg(m_o2Series->count()));
        }
    }
    
    void clearChart()
    {
        m_o2Series->clear();
        m_coSeries->clear();
        m_dataPoints.clear();
        m_statusLabel->setText("图表已清空");
    }

private:
    void setupUI()
    {
        auto *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        auto *layout = new QVBoxLayout(centralWidget);
        
        // 控制按钮
        auto *buttonLayout = new QHBoxLayout();
        auto *startButton = new QPushButton("开始测试", this);
        auto *stopButton = new QPushButton("停止测试", this);
        auto *clearButton = new QPushButton("清空图表", this);
        
        connect(startButton, &QPushButton::clicked, [this]() { m_timer->start(); });
        connect(stopButton, &QPushButton::clicked, [this]() { m_timer->stop(); });
        connect(clearButton, &QPushButton::clicked, this, &ChartPerformanceTest::clearChart);
        
        buttonLayout->addWidget(startButton);
        buttonLayout->addWidget(stopButton);
        buttonLayout->addWidget(clearButton);
        buttonLayout->addStretch();
        
        // 状态标签
        m_statusLabel = new QLabel("准备开始测试", this);
        
        layout->addLayout(buttonLayout);
        layout->addWidget(m_statusLabel);
        layout->addWidget(m_chartView);
        
        setWindowTitle("QtCharts 性能测试 - 10分钟滑动窗口");
        resize(800, 600);
    }
    
    void setupChart()
    {
        m_chart = new QChart();
        m_chartView = new QChartView(m_chart, this);
        m_chartView->setRenderHint(QPainter::Antialiasing);
        
        // 创建数据系列
        m_o2Series = new QLineSeries();
        m_o2Series->setName("O₂ (%)");
        m_o2Series->setColor(QColor("#388e3c"));
        
        m_coSeries = new QLineSeries();
        m_coSeries->setName("CO (ppm)");
        m_coSeries->setColor(QColor("#ff9800"));
        
        m_chart->addSeries(m_o2Series);
        m_chart->addSeries(m_coSeries);
        
        // 设置坐标轴
        m_axisX = new QValueAxis();
        m_axisX->setTitleText("时间 (分钟)");
        m_axisX->setRange(0, 10);  // 10分钟窗口
        m_axisX->setTickCount(6);
        m_axisX->setLabelFormat("%.0f");
        
        m_axisY1 = new QValueAxis();
        m_axisY1->setTitleText("O₂ (%)");
        m_axisY1->setRange(0, 25);
        
        m_axisY2 = new QValueAxis();
        m_axisY2->setTitleText("CO (ppm)");
        m_axisY2->setRange(0, 200);
        
        m_chart->addAxis(m_axisX, Qt::AlignBottom);
        m_chart->addAxis(m_axisY1, Qt::AlignLeft);
        m_chart->addAxis(m_axisY2, Qt::AlignRight);
        
        m_o2Series->attachAxis(m_axisX);
        m_o2Series->attachAxis(m_axisY1);
        m_coSeries->attachAxis(m_axisX);
        m_coSeries->attachAxis(m_axisY2);
        
        m_chart->setTitle("实时数据性能测试 - 优化版本");
    }
    
    void setupTimer()
    {
        m_timer = new QTimer(this);
        m_timer->setInterval(1000);  // 1秒更新一次（模拟快速数据）
        connect(m_timer, &QTimer::timeout, this, &ChartPerformanceTest::addDataPoint);
    }
    
    void updateWithReplace(double timeMinutes, double o2Value, double coValue)
    {
        // 添加到数据点列表
        m_dataPoints.append({timeMinutes, o2Value, coValue});
        
        // 保持10分钟窗口（40个点，每15秒一个点）
        while (m_dataPoints.size() > MAX_WINDOW_POINTS) {
            m_dataPoints.removeFirst();
        }
        
        // 构建QPointF列表
        QList<QPointF> o2Points, coPoints;
        for (const auto &point : m_dataPoints) {
            o2Points.append(QPointF(point.time, point.o2));
            coPoints.append(QPointF(point.time, point.co));
        }
        
        // 使用replace方法更新
        m_o2Series->replace(o2Points);
        m_coSeries->replace(coPoints);
    }
    
    void updateAxisRange(double latestTime)
    {
        const double WINDOW_SIZE = 10.0;  // 10分钟窗口
        
        if (latestTime > WINDOW_SIZE) {
            // 滑动窗口模式
            m_axisX->setRange(latestTime - WINDOW_SIZE, latestTime);
        } else {
            // 初始模式
            m_axisX->setRange(0, WINDOW_SIZE);
        }
    }

private:
    static const int MAX_WINDOW_POINTS = 40;  // 10分钟 / 15秒 = 40个点
    
    struct DataPoint {
        double time;
        double o2;
        double co;
    };
    
    QChartView *m_chartView;
    QChart *m_chart;
    QLineSeries *m_o2Series;
    QLineSeries *m_coSeries;
    QValueAxis *m_axisX;
    QValueAxis *m_axisY1;
    QValueAxis *m_axisY2;
    QTimer *m_timer;
    QLabel *m_statusLabel;
    
    QList<DataPoint> m_dataPoints;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    ChartPerformanceTest window;
    window.show();
    
    return app.exec();
}

#include "chart_performance_test.moc"
